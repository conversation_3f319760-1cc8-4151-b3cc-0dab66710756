const firstPromise = new Promise((resolve,reject) => {
    setTimeout(() => {
        resolve('First Promise resolved');

    }, 1000);
});
const secondPromise = firstPromise.then((message) => {
    console.log(message);
    return new Promise((resolve,reject) => {
        setTimeout,(() => {
            resolve('Second Promise resolved');
        }, 1000);
    });
});
secondPromise.then((message) => {
    console.log(message);
    return new Promise((resolve,reject) => {
        setTimeout(() => {
            resolve('Third Promise resolved');
        }, 1000);
    });
}).then(message) => {
    console.log(message);
});